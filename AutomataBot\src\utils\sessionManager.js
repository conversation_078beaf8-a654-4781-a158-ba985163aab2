// Session management for user interactions

// User session storage
const userSessions = new Map();

/**
 * Get or create user session
 * @param {string} userId - User ID
 * @returns {Object} User session
 */
export function getUserSession(userId) {
  if (!userSessions.has(userId)) {
    console.log(`🔧 [SESSION] Creating new session for user ${userId}`);
    userSessions.set(userId, {
      currentFA: null,
      waitingFor: null,
      lastOperation: null,
      history: [],
      lastActivity: Date.now()
    });
  } else {
    // Update last activity
    const session = userSessions.get(userId);
    session.lastActivity = Date.now();
  }

  const session = userSessions.get(userId);
  console.log(`📋 [SESSION] Retrieved session for user ${userId}: waitingFor=${session.waitingFor}, hasFA=${!!session.currentFA}`);
  return session;
}

/**
 * Update user session
 * @param {string} userId - User ID
 * @param {Object} updates - Updates to apply
 */
export function updateUserSession(userId, updates) {
  const session = getUserSession(userId);
  Object.assign(session, updates);
  session.lastActivity = Date.now();

  console.log(`🔄 [SESSION] Updated session for user ${userId}:`, {
    waitingFor: session.waitingFor,
    hasFA: !!session.currentFA,
    faStates: session.currentFA ? session.currentFA.states?.length : 0,
    updates: Object.keys(updates)
  });
}

/**
 * Clear user session
 * @param {string} userId - User ID
 */
export function clearUserSession(userId) {
  userSessions.delete(userId);
}

/**
 * Get all active sessions count
 * @returns {number} Number of active sessions
 */
export function getActiveSessionsCount() {
  return userSessions.size;
}

/**
 * Clean up old sessions (optional - for memory management)
 * @param {number} maxAge - Maximum age in milliseconds
 */
export function cleanupOldSessions(maxAge = 24 * 60 * 60 * 1000) { // 24 hours
  const now = Date.now();
  let cleanedCount = 0;

  for (const [userId, session] of userSessions.entries()) {
    if (session.lastActivity && (now - session.lastActivity) > maxAge) {
      userSessions.delete(userId);
      cleanedCount++;
      console.log(`🧹 [SESSION] Cleaned up old session for user ${userId}`);
    }
  }

  if (cleanedCount > 0) {
    console.log(`🧹 [SESSION] Cleaned up ${cleanedCount} old sessions. Active sessions: ${userSessions.size}`);
  }

  return cleanedCount;
}

/**
 * Clean up stuck sessions (sessions waiting for input too long)
 * @param {number} maxWaitTime - Maximum wait time in milliseconds
 */
export function cleanupStuckSessions(maxWaitTime = 30 * 60 * 1000) { // 30 minutes
  const now = Date.now();
  let cleanedCount = 0;

  for (const [userId, session] of userSessions.entries()) {
    if (session.waitingFor && session.lastActivity && (now - session.lastActivity) > maxWaitTime) {
      session.waitingFor = null;
      session.lastActivity = now;
      cleanedCount++;
      console.log(`🔄 [SESSION] Cleared stuck session for user ${userId} (was waiting for: ${session.waitingFor})`);
    }
  }

  if (cleanedCount > 0) {
    console.log(`🔄 [SESSION] Cleared ${cleanedCount} stuck sessions`);
  }

  return cleanedCount;
}

/**
 * Get session statistics for monitoring
 */
export function getSessionStats() {
  const stats = {
    totalSessions: userSessions.size,
    waitingSessions: 0,
    sessionsWithFA: 0,
    operationCounts: {}
  };

  for (const [userId, session] of userSessions.entries()) {
    if (session.waitingFor) {
      stats.waitingSessions++;
      stats.operationCounts[session.waitingFor] = (stats.operationCounts[session.waitingFor] || 0) + 1;
    }
    if (session.currentFA) {
      stats.sessionsWithFA++;
    }
  }

  return stats;
}

/**
 * Force clear a user's session (for debugging)
 */
export function forceResetUserSession(userId) {
  if (userSessions.has(userId)) {
    const oldSession = userSessions.get(userId);
    console.log(`🔧 [SESSION] Force resetting session for user ${userId}`, {
      wasWaitingFor: oldSession.waitingFor,
      hadFA: !!oldSession.currentFA
    });

    userSessions.set(userId, {
      currentFA: null,
      waitingFor: null,
      lastOperation: null,
      history: oldSession.history || [],
      lastActivity: Date.now()
    });

    return true;
  }
  return false;
}

/**
 * Validate session state and fix inconsistencies
 */
export function validateAndFixSession(userId) {
  const session = getUserSession(userId);
  let fixed = false;

  // Fix missing lastActivity
  if (!session.lastActivity) {
    session.lastActivity = Date.now();
    fixed = true;
  }

  // Fix missing history array
  if (!Array.isArray(session.history)) {
    session.history = [];
    fixed = true;
  }

  // Check for stuck waiting states (older than 1 hour)
  if (session.waitingFor && session.lastActivity &&
      (Date.now() - session.lastActivity) > 60 * 60 * 1000) {
    console.log(`🔄 [SESSION] Clearing stuck waiting state for user ${userId}: ${session.waitingFor}`);
    session.waitingFor = null;
    session.lastActivity = Date.now();
    fixed = true;
  }

  if (fixed) {
    console.log(`🔧 [SESSION] Fixed session inconsistencies for user ${userId}`);
  }

  return session;
}

/**
 * Safe session operation wrapper
 */
export function safeSessionOperation(userId, operation, operationName = 'unknown') {
  try {
    return operation(validateAndFixSession(userId));
  } catch (error) {
    console.error(`❌ [SESSION] Error in ${operationName} for user ${userId}:`, error);

    // Reset session on critical error
    forceResetUserSession(userId);
    return null;
  }
}
